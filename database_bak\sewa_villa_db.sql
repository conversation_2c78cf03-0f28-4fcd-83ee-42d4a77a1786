-- php<PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.0
-- https://www.phpmyadmin.net/
--
-- Host: localhost:3306
-- Generation Time: Jul 21, 2025 at 12:04 PM
-- Server version: 5.7.33
-- PHP Version: 7.4.19

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `kolam_renang_db_03_2025`
--

-- --------------------------------------------------------

--
-- Table structure for table `detail_sewa_loker`
--

CREATE TABLE `detail_sewa_loker` (
  `id_detail_sewa` int(11) NOT NULL,
  `kode_struk` int(11) DEFAULT NULL,
  `nomor_loker` int(11) DEFAULT NULL,
  `harga_sewa` int(12) DEFAULT NULL,
  `status_kunci` enum('dipinjam','dikembalikan') DEFAULT 'dipinjam',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Triggers `detail_sewa_loker`
--
DELIMITER $$
CREATE TRIGGER `update_status_loker_kembali` AFTER UPDATE ON `detail_sewa_loker` FOR EACH ROW BEGIN
    IF NEW.status_kunci = 'dikembalikan' THEN
        UPDATE loker SET status = 'tersedia'
        WHERE nomor_loker = NEW.nomor_loker;
    END IF;
END
$$
DELIMITER ;
DELIMITER $$
CREATE TRIGGER `update_status_loker_sewa` AFTER INSERT ON `detail_sewa_loker` FOR EACH ROW BEGIN
    UPDATE loker SET status = 'disewa'
    WHERE nomor_loker = NEW.nomor_loker;
END
$$
DELIMITER ;

-- --------------------------------------------------------

--
-- Table structure for table `item_harga`
--

CREATE TABLE `item_harga` (
  `id` int(11) NOT NULL,
  `nama_item` varchar(50) DEFAULT NULL,
  `jenis_item` enum('tiket','loker','lain-lain','glamping','playground') NOT NULL,
  `warna_item` enum('green','teal','blue','orange','grey','pink','primary','success') NOT NULL DEFAULT 'grey',
  `harga` int(10) DEFAULT NULL,
  `keterangan` text,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `item_harga`
--

INSERT INTO `item_harga` (`id`, `nama_item`, `jenis_item`, `warna_item`, `harga`, `keterangan`, `created_at`) VALUES
(5, 'tiket_playground', 'playground', 'success', 20000, 'Tiket masuk playground untuk anak-anak', '2025-07-18 18:15:16'),
(11, 'tiket_glamping', 'glamping', 'primary', 500000, 'Tenda glamping keluarga untuk 1 malam (max 4 orang)', '2025-07-18 18:15:16');

-- --------------------------------------------------------

--
-- Table structure for table `loker`
--

CREATE TABLE `loker` (
  `id_loker` int(11) NOT NULL,
  `nomor_loker` int(11) NOT NULL,
  `status` enum('tersedia','disewa','maintenance') DEFAULT 'tersedia',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

-- --------------------------------------------------------

--
-- Table structure for table `profile_outlet`
--

CREATE TABLE `profile_outlet` (
  `id` int(10) NOT NULL,
  `outlet_name` varchar(50) DEFAULT NULL,
  `outlet_code` varchar(10) DEFAULT NULL,
  `address` varchar(100) DEFAULT NULL,
  `phone` varchar(50) DEFAULT NULL,
  `email` varchar(50) DEFAULT NULL,
  `default_kasir` int(11) DEFAULT NULL,
  `active_status` varchar(20) DEFAULT 'active',
  `del_status` varchar(10) DEFAULT 'Live'
) ENGINE=InnoDB DEFAULT CHARSET=utf8;

--
-- Dumping data for table `profile_outlet`
--

INSERT INTO `profile_outlet` (`id`, `outlet_name`, `outlet_code`, `address`, `phone`, `email`, `default_kasir`, `active_status`, `del_status`) VALUES
(1, 'Kolam Renang &amp; Resto Lembah Cinta 99', '000001', 'Jl. Gn. Lempuyang, No 99X', '087762010373', '<EMAIL>', 0, 'active', 'Live');

-- --------------------------------------------------------

--
-- Table structure for table `struk_pembayaran`
--

CREATE TABLE `struk_pembayaran` (
  `kode_struk` int(11) NOT NULL,
  `no_hp` varchar(15) DEFAULT NULL,
  `tanggal` datetime DEFAULT NULL,
  `jumlah_tiket` int(11) DEFAULT NULL,
  `total_harga` int(10) DEFAULT NULL,
  `jumlah_bayar` int(15) NOT NULL,
  `kembalian` int(15) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `metode_bayar` enum('tunai','transfer','qris') NOT NULL DEFAULT 'tunai',
  `status` enum('aktif','delete') NOT NULL DEFAULT 'aktif',
  `kasir` varchar(70) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `struk_pembayaran`
--

INSERT INTO `struk_pembayaran` (`kode_struk`, `no_hp`, `tanggal`, `jumlah_tiket`, `total_harga`, `jumlah_bayar`, `kembalian`, `created_at`, `metode_bayar`, `status`, `kasir`) VALUES
(1, NULL, '2025-07-21 12:43:28', 0, 520000, 620000, 100000, '2025-07-21 04:43:28', 'tunai', 'aktif', 'admin'),
(2, NULL, '2025-07-21 14:11:34', 0, 520000, 1000000, 480000, '2025-07-21 06:11:34', 'tunai', 'aktif', 'admin');

-- --------------------------------------------------------

--
-- Table structure for table `struk_pembayaran_detail`
--

CREATE TABLE `struk_pembayaran_detail` (
  `id_detail` int(11) NOT NULL,
  `kode_struk` int(11) NOT NULL,
  `id_item` int(11) NOT NULL,
  `nama_item` varchar(150) NOT NULL,
  `jenis_item` varchar(10) NOT NULL DEFAULT 'tiket',
  `jumlah` int(11) NOT NULL,
  `harga_satuan` int(10) NOT NULL,
  `subtotal` decimal(15,0) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `struk_pembayaran_detail`
--

INSERT INTO `struk_pembayaran_detail` (`id_detail`, `kode_struk`, `id_item`, `nama_item`, `jenis_item`, `jumlah`, `harga_satuan`, `subtotal`, `created_at`) VALUES
(1, 1, 5, 'Tiket Playground', '', 1, 20000, '20000', '2025-07-21 04:43:28'),
(2, 1, 11, 'Tiket Glamping', '', 1, 500000, '500000', '2025-07-21 04:43:28'),
(3, 2, 5, 'Tiket Playground', 'playground', 1, 20000, '20000', '2025-07-21 06:11:34'),
(4, 2, 11, 'Tiket Glamping', 'glamping', 1, 500000, '500000', '2025-07-21 06:11:34');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id_user` int(11) NOT NULL,
  `username` varchar(50) NOT NULL,
  `password` varchar(255) NOT NULL,
  `nama_lengkap` varchar(100) NOT NULL,
  `email` varchar(100) NOT NULL,
  `role` enum('admin','kasir') DEFAULT 'kasir',
  `is_active` tinyint(1) DEFAULT '1',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `last_login` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=latin1;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id_user`, `username`, `password`, `nama_lengkap`, `email`, `role`, `is_active`, `created_at`, `last_login`) VALUES
(1, 'admin', '$2y$10$6F.Aaey1Cds2e0ma.6mcmuzEA.wxcQCa7XNFSxaN8Dy6VAVimQuAu', 'Administrator', '<EMAIL>', 'admin', 1, '2024-12-19 08:34:44', '2025-07-21 02:28:02'),
(3, 'tangkas', '$2y$10$IQR3KpHtS4xosiOTQKuwM.JpGAKw/ONjPgdUmB5.Sf8Qb9eC7S/s2', 'gede tangkas suadnyana', '<EMAIL>', 'kasir', 1, '2024-12-29 03:45:50', '2025-03-23 06:23:18'),
(4, 'tester', '$2y$10$TaWQ5Cxrtx5kXK6a3myHqOEfBNS.ICNeyxqxfYu6bLyeI2Y80LRBq', 'test', 'test@asdasd', 'kasir', 1, '2025-03-26 16:30:43', '2025-07-21 02:27:16');

--
-- Indexes for dumped tables
--

--
-- Indexes for table `detail_sewa_loker`
--
ALTER TABLE `detail_sewa_loker`
  ADD PRIMARY KEY (`id_detail_sewa`),
  ADD KEY `nomor_loker` (`nomor_loker`),
  ADD KEY `detail_sewa_loker_ibfk_1` (`kode_struk`);

--
-- Indexes for table `item_harga`
--
ALTER TABLE `item_harga`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `loker`
--
ALTER TABLE `loker`
  ADD PRIMARY KEY (`id_loker`),
  ADD UNIQUE KEY `nomor_loker` (`nomor_loker`);

--
-- Indexes for table `profile_outlet`
--
ALTER TABLE `profile_outlet`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `struk_pembayaran`
--
ALTER TABLE `struk_pembayaran`
  ADD PRIMARY KEY (`kode_struk`);

--
-- Indexes for table `struk_pembayaran_detail`
--
ALTER TABLE `struk_pembayaran_detail`
  ADD PRIMARY KEY (`id_detail`),
  ADD KEY `kode_struk` (`kode_struk`),
  ADD KEY `id_item` (`id_item`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id_user`),
  ADD UNIQUE KEY `username` (`username`),
  ADD UNIQUE KEY `email` (`email`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `detail_sewa_loker`
--
ALTER TABLE `detail_sewa_loker`
  MODIFY `id_detail_sewa` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `item_harga`
--
ALTER TABLE `item_harga`
  MODIFY `id` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=14;

--
-- AUTO_INCREMENT for table `loker`
--
ALTER TABLE `loker`
  MODIFY `id_loker` int(11) NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `profile_outlet`
--
ALTER TABLE `profile_outlet`
  MODIFY `id` int(10) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `struk_pembayaran`
--
ALTER TABLE `struk_pembayaran`
  MODIFY `kode_struk` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `struk_pembayaran_detail`
--
ALTER TABLE `struk_pembayaran_detail`
  MODIFY `id_detail` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id_user` int(11) NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
