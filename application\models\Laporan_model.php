<?php
defined('BASEPATH') or exit('No direct script access allowed');

class Laporan_model extends CI_Model
{
    public function get_laporan_harian($start_date, $end_date)
    {

        if ($this->session->userdata('role') != 'admin') {
            $this->db->select('s.*,  <PERSON><PERSON>(e.jumlah) as jml_item ')
                ->from('struk_pembayaran s')
                ->join('struk_pembayaran_detail e', 's.kode_struk = e.kode_struk', 'left')
                ->where('DATE(s.tanggal) >=', $start_date)
                ->where('DATE(s.tanggal) <=', $end_date)
                ->where('s.status', 'aktif')

                ->where('s.kasir', $this->session->userdata('username'))
                ->order_by('s.tanggal', 'DESC')->group_by('s.kode_struk');
        } else {
            $this->db->select('s.*,  <PERSON><PERSON>(e.jumlah) as jml_item ')
            ->from('struk_pembayaran s')
            ->join('struk_pembayaran_detail e', 's.kode_struk = e.kode_struk', 'left')
            ->where('DATE(s.tanggal) >=', $start_date)
            ->where('DATE(s.tanggal) <=', $end_date)
            ->where('s.status', 'aktif')
            ->order_by('s.tanggal', 'DESC')->group_by('s.kode_struk');
        }


        return $this->db->get()->result();
    }

    public function get_laporan_bulanan($bulan)
    {
        if ($this->session->userdata('role') != 'admin') {
        $this->db->where("DATE_FORMAT(tanggal, '%Y-%m') =", $bulan)
            ->where('kasir', $this->session->userdata('username'))
            ->where('status', 'aktif')
            ->order_by('tanggal', 'DESC');
        } else {
            $this->db->where("DATE_FORMAT(tanggal, '%Y-%m') =", $bulan)
            ->where('status', 'aktif')
            ->order_by('tanggal', 'DESC');
        }
        return $this->db->get('struk_pembayaran')->result();
    }

    // Laporan Playground - Harian
    public function get_laporan_playground_harian($start_date, $end_date)
    {
        if ($this->session->userdata('role') != 'admin') {
            $this->db->select('s.*, SUM(e.jumlah) as jml_item, SUM(e.subtotal) as total_playground')
                ->from('struk_pembayaran s')
                ->join('struk_pembayaran_detail e', 's.kode_struk = e.kode_struk', 'left')
                ->where('DATE(s.tanggal) >=', $start_date)
                ->where('DATE(s.tanggal) <=', $end_date)
                ->where('s.status', 'aktif')
                ->where('e.jenis_item', 'playground')
                ->where('s.kasir', $this->session->userdata('username'))
                ->order_by('s.tanggal', 'DESC')
                ->group_by('s.kode_struk');
        } else {
            $this->db->select('s.*, SUM(e.jumlah) as jml_item, SUM(e.subtotal) as total_playground')
                ->from('struk_pembayaran s')
                ->join('struk_pembayaran_detail e', 's.kode_struk = e.kode_struk', 'left')
                ->where('DATE(s.tanggal) >=', $start_date)
                ->where('DATE(s.tanggal) <=', $end_date)
                ->where('s.status', 'aktif')
                ->where('e.jenis_item', 'playground')
                ->order_by('s.tanggal', 'DESC')
                ->group_by('s.kode_struk');
        }

        return $this->db->get()->result();
    }

    // Laporan Playground - Bulanan
    public function get_laporan_playground_bulanan($bulan)
    {
        if ($this->session->userdata('role') != 'admin') {
            $this->db->select('s.*, SUM(e.jumlah) as jml_item, SUM(e.subtotal) as total_playground')
                ->from('struk_pembayaran s')
                ->join('struk_pembayaran_detail e', 's.kode_struk = e.kode_struk', 'left')
                ->where("DATE_FORMAT(s.tanggal, '%Y-%m') =", $bulan)
                ->where('s.kasir', $this->session->userdata('username'))
                ->where('s.status', 'aktif')
                ->where('e.jenis_item', 'playground')
                ->order_by('s.tanggal', 'DESC')
                ->group_by('s.kode_struk');
        } else {
            $this->db->select('s.*, SUM(e.jumlah) as jml_item, SUM(e.subtotal) as total_playground')
                ->from('struk_pembayaran s')
                ->join('struk_pembayaran_detail e', 's.kode_struk = e.kode_struk', 'left')
                ->where("DATE_FORMAT(s.tanggal, '%Y-%m') =", $bulan)
                ->where('s.status', 'aktif')
                ->where('e.jenis_item', 'playground')
                ->order_by('s.tanggal', 'DESC')
                ->group_by('s.kode_struk');
        }
        
        return $this->db->get()->result();
    }

    // Laporan Glamping - Harian
    public function get_laporan_glamping_harian($start_date, $end_date)
    {
        if ($this->session->userdata('role') != 'admin') {
            $this->db->select('s.*, SUM(e.jumlah) as jml_item, SUM(e.subtotal) as total_glamping')
                ->from('struk_pembayaran s')
                ->join('struk_pembayaran_detail e', 's.kode_struk = e.kode_struk', 'left')
                ->where('DATE(s.tanggal) >=', $start_date)
                ->where('DATE(s.tanggal) <=', $end_date)
                ->where('s.status', 'aktif')
                ->where('e.jenis_item', 'glamping')
                ->where('s.kasir', $this->session->userdata('username'))
                ->order_by('s.tanggal', 'DESC')
                ->group_by('s.kode_struk');
        } else {
            $this->db->select('s.*, SUM(e.jumlah) as jml_item, SUM(e.subtotal) as total_glamping')
                ->from('struk_pembayaran s')
                ->join('struk_pembayaran_detail e', 's.kode_struk = e.kode_struk', 'left')
                ->where('DATE(s.tanggal) >=', $start_date)
                ->where('DATE(s.tanggal) <=', $end_date)
                ->where('s.status', 'aktif')
                ->where('e.jenis_item', 'glamping')
                ->order_by('s.tanggal', 'DESC')
                ->group_by('s.kode_struk');
        }

        return $this->db->get()->result();
    }

    // Laporan Glamping - Bulanan
    public function get_laporan_glamping_bulanan($bulan)
    {
        if ($this->session->userdata('role') != 'admin') {
            $this->db->select('s.*, SUM(e.jumlah) as jml_item, SUM(e.subtotal) as total_glamping')
                ->from('struk_pembayaran s')
                ->join('struk_pembayaran_detail e', 's.kode_struk = e.kode_struk', 'left')
                ->where("DATE_FORMAT(s.tanggal, '%Y-%m') =", $bulan)
                ->where('s.kasir', $this->session->userdata('username'))
                ->where('s.status', 'aktif')
                ->where('e.jenis_item', 'glamping')
                ->order_by('s.tanggal', 'DESC')
                ->group_by('s.kode_struk');
        } else {
            $this->db->select('s.*, SUM(e.jumlah) as jml_item, SUM(e.subtotal) as total_glamping')
                ->from('struk_pembayaran s')
                ->join('struk_pembayaran_detail e', 's.kode_struk = e.kode_struk', 'left')
                ->where("DATE_FORMAT(s.tanggal, '%Y-%m') =", $bulan)
                ->where('s.status', 'aktif')
                ->where('e.jenis_item', 'glamping')
                ->order_by('s.tanggal', 'DESC')
                ->group_by('s.kode_struk');
        }
        
        return $this->db->get()->result();
    }
}
